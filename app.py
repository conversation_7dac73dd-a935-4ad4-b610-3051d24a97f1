import os
import uuid
import json
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, session, send_file
from werkzeug.utils import secure_filename
from functools import wraps
from config import Config
from utils import (
    allowed_file,
    is_pdf_file,
    create_team_folder,
    save_submission_metadata,
    validate_form_data
)

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize app
    Config.init_app(app)
    
    return app

app = create_app()

def admin_required(f):
    """Decorator to require admin authentication for routes."""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not session.get('admin_logged_in'):
            return redirect(url_for('admin_login'))
        return f(*args, **kwargs)
    return decorated_function

@app.route('/', methods=['GET', 'POST'])
def index():
    if request.method == 'POST':
        # Validate form data
        form_errors = validate_form_data(request.form)
        
        # Check if file was uploaded
        if 'proposal_file' not in request.files:
            form_errors.append('No file was uploaded.')
        else:
            file = request.files['proposal_file']
            if file.filename == '':
                form_errors.append('No file was selected.')
            elif not allowed_file(file.filename):
                form_errors.append('Only PDF files are allowed.')
        
        # If there are validation errors, show them
        if form_errors:
            for error in form_errors:
                flash(error, 'error')
            return render_template('index.html')
        
        # Process the file upload
        file = request.files['proposal_file']
        
        try:
            # Create team folder
            team_folder_path, folder_name = create_team_folder(request.form['team_name'])
            
            # Generate secure filename
            original_filename = file.filename
            secure_name = secure_filename(original_filename)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            final_filename = f"{timestamp}_{secure_name}"
            file_path = os.path.join(team_folder_path, final_filename)
            
            # Save the file
            file.save(file_path)
            
            # Verify it's actually a PDF
            if not is_pdf_file(file_path):
                os.remove(file_path)  # Remove the invalid file
                os.rmdir(team_folder_path)  # Remove empty folder
                flash('The uploaded file is not a valid PDF. Please upload a PDF file.', 'error')
                return render_template('index.html')
            
            # Get file size for display
            file_size = os.path.getsize(file_path)
            file_size_mb = round(file_size / (1024 * 1024), 2)
            
            # Generate submission ID
            submission_id = str(uuid.uuid4())[:8].upper()
            
            # Prepare submission data
            submission_data = {
                'submission_id': submission_id,
                'team_name': request.form['team_name'].strip(),
                'college_name': request.form['college_name'].strip(),
                'proposed_title': request.form['proposed_title'].strip(),
                'original_filename': original_filename,
                'saved_filename': final_filename,
                'file_path': file_path,
                'folder_name': folder_name,
                'file_size': f"{file_size_mb} MB",
                'file_size_bytes': file_size,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'ip_address': request.remote_addr
            }
            
            # Save metadata
            save_submission_metadata(submission_data)
            
            flash('Your proposal has been successfully submitted!', 'success')
            return render_template('success.html', submission=submission_data)
            
        except Exception as e:
            flash(f'An error occurred while processing your submission: {str(e)}', 'error')
            return render_template('index.html')
    
    return render_template('index.html')

@app.route('/admin/login', methods=['GET', 'POST'])
def admin_login():
    """Admin login page."""
    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '').strip()

        # Debug information (remove in production)
        print(f"Login attempt - Username: '{username}', Password: '{password}'")
        print(f"Expected - Username: '{Config.ADMIN_USERNAME}', Password: '{Config.ADMIN_PASSWORD}'")
        print(f"Username match: {username == Config.ADMIN_USERNAME}")
        print(f"Password match: {password == Config.ADMIN_PASSWORD}")

        if username == Config.ADMIN_USERNAME and password == Config.ADMIN_PASSWORD:
            session['admin_logged_in'] = True
            flash('Successfully logged in as admin.', 'success')
            return redirect(url_for('admin'))
        else:
            flash(f'Invalid username or password. Please use: {Config.ADMIN_USERNAME} / {Config.ADMIN_PASSWORD}', 'error')

    return render_template('admin_login.html')

@app.route('/admin/logout')
def admin_logout():
    """Admin logout."""
    session.pop('admin_logged_in', None)
    flash('You have been logged out.', 'info')
    return redirect(url_for('index'))

@app.route('/admin')
@admin_required
def admin():
    """Admin dashboard to view submissions."""
    try:
        json_file_path = os.path.join(Config.DATA_FOLDER, 'submissions.json')

        if os.path.exists(json_file_path):
            with open(json_file_path, 'r', encoding='utf-8') as f:
                submissions = json.load(f)
        else:
            submissions = []

        # Calculate statistics
        total_size_bytes = sum(sub.get('file_size_bytes', 0) for sub in submissions)
        total_size = f"{round(total_size_bytes / (1024 * 1024), 2)} MB" if total_size_bytes > 0 else "0 MB"

        unique_colleges = len(set(sub.get('college_name', '') for sub in submissions))

        latest_submission = "None"
        if submissions:
            latest = max(submissions, key=lambda x: x.get('timestamp', ''))
            latest_submission = latest.get('timestamp', 'Unknown')[:10]  # Just the date

        return render_template('admin_dashboard.html',
                             submissions=submissions,
                             total_size=total_size,
                             unique_colleges=unique_colleges,
                             latest_submission=latest_submission)

    except Exception as e:
        flash(f'Error loading submissions: {str(e)}', 'error')
        return render_template('admin_dashboard.html', submissions=[])

@app.route('/admin/download/<submission_id>')
@admin_required
def download_file(submission_id):
    """Download a submitted file."""
    try:
        json_file_path = os.path.join(Config.DATA_FOLDER, 'submissions.json')

        if os.path.exists(json_file_path):
            with open(json_file_path, 'r', encoding='utf-8') as f:
                submissions = json.load(f)

            # Find the submission
            submission = next((sub for sub in submissions if sub['submission_id'] == submission_id), None)

            if submission and os.path.exists(submission['file_path']):
                return send_file(
                    submission['file_path'],
                    as_attachment=True,
                    download_name=submission['original_filename']
                )
            else:
                flash('File not found.', 'error')
        else:
            flash('No submissions found.', 'error')

    except Exception as e:
        flash(f'Error downloading file: {str(e)}', 'error')

    return redirect(url_for('admin'))

@app.errorhandler(413)
def too_large(e):
    flash('File is too large. Maximum file size is 16MB.', 'error')
    return redirect(url_for('index'))

@app.errorhandler(404)
def not_found(e):
    return render_template('index.html'), 404

@app.errorhandler(500)
def server_error(e):
    flash('An internal server error occurred. Please try again.', 'error')
    return render_template('index.html'), 500

if __name__ == '__main__':
    print("🚀 Starting IIC Quest 3.0 Proposal Submission System...")
    print(f"📁 Upload folder: {Config.UPLOAD_FOLDER}")
    print(f"📊 Data folder: {Config.DATA_FOLDER}")
    print("🌐 Server will be available at: http://localhost:5000")
    print("👨‍💼 Admin panel available at: http://localhost:5000/admin")
    print(f"🔐 Admin login: {Config.ADMIN_USERNAME} / {Config.ADMIN_PASSWORD}")
    print("=" * 50)

    app.run(debug=True, host='0.0.0.0', port=5000)
