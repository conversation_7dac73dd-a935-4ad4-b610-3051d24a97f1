# IIC Quest 3.0 Proposal Submission System

A web-based proposal submission system for the IIC Quest 3.0 hackathon. This application allows participants to submit their hackathon proposals through a user-friendly web interface.

## Features

- **Web-based submission form** with required fields:
  - Team Name
  - College Name  
  - Proposed Title
  - PDF file upload
- **File validation** - Only PDF files accepted (max 16MB)
- **Organized storage** - Files stored in timestamped folders
- **Metadata tracking** - All submission details saved in JSON format
- **Responsive design** - Works on desktop and mobile devices
- **Admin panel** - View all submissions at `/admin`

## Quick Start

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the application:**
   ```bash
   python app.py
   ```

3. **Access the application:**
   - Main submission form: http://localhost:5000
   - Admin panel: http://localhost:5000/admin
   - **Admin credentials:** username: `admin`, password: `iic2024admin`

## File Structure

```
ftp-server/
├── app.py                 # Main Flask application
├── config.py             # Configuration settings
├── utils.py              # Utility functions
├── requirements.txt      # Python dependencies
├── templates/            # HTML templates
│   ├── base.html
│   ├── index.html
│   └── success.html
├── static/              # CSS and static files
│   └── style.css
├── uploads/             # Uploaded files (auto-created)
└── data/               # Submission metadata (auto-created)
    └── submissions.json
```

## Configuration

Edit `config.py` to modify:
- Maximum file size (default: 16MB)
- Upload folder location
- Application settings

## Submission Data

Each submission includes:
- Unique submission ID
- Team and college information
- Proposal title and file
- Timestamp and IP address
- File metadata

## Security Features

- File type validation (PDF only)
- File size limits
- Secure filename handling
- Input sanitization
- Error handling

## Admin Features

The admin panel is protected with authentication. Default credentials:
- **Username:** `admin`
- **Password:** `iic2024admin`

You can change these by setting environment variables:
```bash
export ADMIN_USERNAME=your_username
export ADMIN_PASSWORD=your_password
```

Admin dashboard features:
- View all submissions in a table format
- Download submitted PDF files
- View submission statistics
- Detailed submission information modal
- Secure logout functionality

## Troubleshooting

- Ensure Python 3.7+ is installed
- Check that all dependencies are installed
- Verify file permissions for upload directory
- Check firewall settings if accessing from other devices

## Support

For issues or questions about the IIC Quest 3.0 hackathon, contact the organizing committee.
