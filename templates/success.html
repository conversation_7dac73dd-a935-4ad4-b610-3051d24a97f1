{% extends "base.html" %}

{% block title %}Submission Successful - {{ config.APP_NAME }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow border-success">
            <div class="card-header bg-success text-white">
                <h2 class="card-title mb-0">
                    <i class="fas fa-check-circle me-2"></i>
                    Submission Successful!
                </h2>
            </div>
            <div class="card-body">
                <div class="alert alert-success">
                    <i class="fas fa-thumbs-up me-2"></i>
                    <strong>Congratulations!</strong> Your proposal has been successfully submitted.
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <h5><i class="fas fa-info-circle me-2"></i>Submission Details</h5>
                        <ul class="list-unstyled">
                            <li><strong>Team Name:</strong> {{ submission.team_name }}</li>
                            <li><strong>College:</strong> {{ submission.college_name }}</li>
                            <li><strong>Proposed Title:</strong> {{ submission.proposed_title }}</li>
                            <li><strong>Submitted:</strong> {{ submission.timestamp }}</li>
                            <li><strong>Submission ID:</strong> {{ submission.submission_id }}</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h5><i class="fas fa-file-pdf me-2"></i>File Information</h5>
                        <ul class="list-unstyled">
                            <li><strong>Filename:</strong> {{ submission.original_filename }}</li>
                            <li><strong>File Size:</strong> {{ submission.file_size }}</li>
                            <li><strong>Status:</strong> <span class="badge bg-success">Uploaded</span></li>
                        </ul>
                    </div>
                </div>

                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>What's Next?</strong>
                    <ul class="mb-0 mt-2">
                        <li>Your proposal will be reviewed by the organizing committee</li>
                        <li>Keep your submission ID for future reference</li>
                        <li>Results will be announced as per the hackathon schedule</li>
                        <li>Contact the organizers if you have any questions</li>
                    </ul>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-center mt-4">
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Submit Another Proposal
                    </a>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <p class="text-muted">
                <i class="fas fa-envelope me-1"></i>
                For any queries, please contact the {{ config.HACKATHON_NAME }} organizing committee.
            </p>
        </div>
    </div>
</div>
{% endblock %}
