{% extends "base.html" %}

{% block title %}Admin Login - {{ config.APP_NAME }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-warning text-dark">
                <h3 class="card-title mb-0 text-center">
                    <i class="fas fa-shield-alt me-2"></i>
                    Admin Access
                </h3>
            </div>
            <div class="card-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Restricted Area:</strong> Admin credentials required to access submission data.
                </div>

                <form method="POST">
                    <div class="mb-3">
                        <label for="username" class="form-label">
                            <i class="fas fa-user me-1"></i>Username
                        </label>
                        <input type="text" class="form-control" id="username" name="username" 
                               value="{{ request.form.get('username', '') }}" required>
                    </div>

                    <div class="mb-4">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-1"></i>Password
                        </label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Login to Admin Panel
                        </button>
                    </div>
                </form>

                <div class="text-center mt-3">
                    <a href="{{ url_for('index') }}" class="text-muted">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to Submission Form
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
