{% extends "base.html" %}

{% block title %}Submit Proposal - {{ config.APP_NAME }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h2 class="card-title mb-0">
                    <i class="fas fa-upload me-2"></i>
                    Submit Your Proposal
                </h2>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Instructions:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Fill in all required fields below</li>
                        <li>Upload your proposal as a PDF file (max 16MB)</li>
                        <li>Ensure your team name and college name are accurate</li>
                        <li>Double-check your proposed title before submitting</li>
                    </ul>
                </div>

                <form method="POST" enctype="multipart/form-data" id="proposalForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="team_name" class="form-label">
                                <i class="fas fa-users me-1"></i>Team Name *
                            </label>
                            <input type="text" class="form-control" id="team_name" name="team_name" 
                                   value="{{ request.form.get('team_name', '') }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="college_name" class="form-label">
                                <i class="fas fa-university me-1"></i>College Name *
                            </label>
                            <input type="text" class="form-control" id="college_name" name="college_name" 
                                   value="{{ request.form.get('college_name', '') }}" required>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="proposed_title" class="form-label">
                            <i class="fas fa-lightbulb me-1"></i>Proposed Title *
                        </label>
                        <input type="text" class="form-control" id="proposed_title" name="proposed_title" 
                               value="{{ request.form.get('proposed_title', '') }}" required>
                        <div class="form-text">Enter the title of your hackathon project proposal</div>
                    </div>

                    <div class="mb-4">
                        <label for="proposal_file" class="form-label">
                            <i class="fas fa-file-pdf me-1"></i>Proposal Document *
                        </label>
                        <input type="file" class="form-control" id="proposal_file" name="proposal_file" 
                               accept=".pdf" required>
                        <div class="form-text">
                            Upload your proposal as a PDF file. Maximum file size: 16MB
                        </div>
                    </div>

                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>
                            Submit Proposal
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="text-center mt-4">
            <p class="text-muted">
                <i class="fas fa-shield-alt me-1"></i>
                Your submission will be securely stored and reviewed by the organizing committee.
            </p>
        </div>
    </div>
</div>

<script>
document.getElementById('proposalForm').addEventListener('submit', function(e) {
    const fileInput = document.getElementById('proposal_file');
    const file = fileInput.files[0];
    
    if (file) {
        // Check file size (16MB = 16 * 1024 * 1024 bytes)
        if (file.size > 16 * 1024 * 1024) {
            e.preventDefault();
            alert('File size must be less than 16MB. Please choose a smaller file.');
            return;
        }
        
        // Check file type
        if (!file.name.toLowerCase().endsWith('.pdf')) {
            e.preventDefault();
            alert('Please upload a PDF file only.');
            return;
        }
    }
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
    submitBtn.disabled = true;
});
</script>
{% endblock %}
