{% extends "base.html" %}

{% block title %}Admin Dashboard - {{ config.APP_NAME }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h2>
        <i class="fas fa-tachometer-alt me-2"></i>
        Admin Dashboard
    </h2>
    <div>
        <a href="{{ url_for('admin_logout') }}" class="btn btn-outline-danger">
            <i class="fas fa-sign-out-alt me-2"></i>Logout
        </a>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ submissions|length }}</h4>
                        <p class="mb-0">Total Submissions</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file-upload fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ total_size }}</h4>
                        <p class="mb-0">Total Storage</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-hdd fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ unique_colleges }}</h4>
                        <p class="mb-0">Colleges</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-university fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h4>{{ latest_submission }}</h4>
                        <p class="mb-0">Latest Submission</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <h4 class="mb-0">
            <i class="fas fa-list me-2"></i>
            All Submissions
        </h4>
    </div>
    <div class="card-body">
        {% if submissions %}
        <div class="mb-3">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                Click <strong>Details</strong> to view submission info,
                <strong>Preview</strong> to view PDF in modal,
                <strong>External Link</strong> to open in new tab, or
                <strong>Download</strong> to save the file.
            </small>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>ID</th>
                        <th>Team Name</th>
                        <th>College</th>
                        <th>Proposed Title</th>
                        <th>File</th>
                        <th>Size</th>
                        <th>Submitted</th>
                        <th width="150">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for submission in submissions %}
                    <tr>
                        <td>
                            <span class="badge bg-primary">{{ submission.submission_id }}</span>
                        </td>
                        <td>
                            <strong>{{ submission.team_name }}</strong>
                        </td>
                        <td>{{ submission.college_name }}</td>
                        <td>
                            <span class="text-truncate d-inline-block" style="max-width: 200px;" 
                                  title="{{ submission.proposed_title }}">
                                {{ submission.proposed_title }}
                            </span>
                        </td>
                        <td>
                            <i class="fas fa-file-pdf text-danger me-1"></i>
                            {{ submission.original_filename }}
                        </td>
                        <td>{{ submission.file_size }}</td>
                        <td>
                            <small>{{ submission.timestamp }}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-info btn-sm"
                                        onclick="showDetails('{{ submission.submission_id }}')"
                                        title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button class="btn btn-outline-primary btn-sm"
                                        onclick="previewPDF('{{ submission.submission_id }}', '{{ submission.original_filename }}')"
                                        title="Preview PDF">
                                    <i class="fas fa-file-pdf"></i>
                                </button>
                                <a href="{{ url_for('preview_file', submission_id=submission.submission_id) }}"
                                   class="btn btn-outline-secondary btn-sm" title="Open PDF in New Tab" target="_blank">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                                <a href="{{ url_for('download_file', submission_id=submission.submission_id) }}"
                                   class="btn btn-outline-success btn-sm" title="Download">
                                    <i class="fas fa-download"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">No submissions yet</h5>
            <p class="text-muted">Submissions will appear here once participants start uploading their proposals.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Details Modal -->
<div class="modal fade" id="detailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Submission Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Details will be loaded here -->
            </div>
        </div>
    </div>
</div>

<!-- PDF Preview Modal -->
<div class="modal fade" id="pdfPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-fullscreen">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="pdfModalTitle">
                    <i class="fas fa-file-pdf me-2"></i>PDF Preview
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-0">
                <iframe id="pdfFrame" src="" width="100%" height="600" style="border: none;">
                    <p>Your browser does not support PDFs.
                       <a href="" id="pdfDownloadLink">Download the PDF</a> instead.
                    </p>
                </iframe>
            </div>
            <div class="modal-footer">
                <a href="" id="pdfOpenNewTab" class="btn btn-outline-primary" target="_blank">
                    <i class="fas fa-external-link-alt me-2"></i>Open in New Tab
                </a>
                <a href="" id="pdfDownloadBtn" class="btn btn-success">
                    <i class="fas fa-download me-2"></i>Download PDF
                </a>
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<script>
function showDetails(submissionId) {
    // Find the submission data
    const submissions = {{ submissions | tojson }};
    const submission = submissions.find(s => s.submission_id === submissionId);

    if (submission) {
        const modalBody = document.getElementById('modalBody');
        modalBody.innerHTML = `
            <div class="row">
                <div class="col-md-6">
                    <h6><i class="fas fa-info-circle me-2"></i>Basic Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Submission ID:</strong></td><td>${submission.submission_id}</td></tr>
                        <tr><td><strong>Team Name:</strong></td><td>${submission.team_name}</td></tr>
                        <tr><td><strong>College:</strong></td><td>${submission.college_name}</td></tr>
                        <tr><td><strong>Proposed Title:</strong></td><td>${submission.proposed_title}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6><i class="fas fa-file me-2"></i>File Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Original Filename:</strong></td><td>${submission.original_filename}</td></tr>
                        <tr><td><strong>File Size:</strong></td><td>${submission.file_size}</td></tr>
                        <tr><td><strong>Submitted:</strong></td><td>${submission.timestamp}</td></tr>
                        <tr><td><strong>IP Address:</strong></td><td>${submission.ip_address || 'N/A'}</td></tr>
                    </table>
                </div>
            </div>
        `;

        const modal = new bootstrap.Modal(document.getElementById('detailsModal'));
        modal.show();
    }
}

function previewPDF(submissionId, filename) {
    // Set up the PDF preview modal
    const previewUrl = `/admin/preview/${submissionId}`;
    const downloadUrl = `/admin/download/${submissionId}`;

    // Update modal title
    document.getElementById('pdfModalTitle').innerHTML =
        `<i class="fas fa-file-pdf me-2"></i>PDF Preview - ${filename}`;

    // Set iframe source
    document.getElementById('pdfFrame').src = previewUrl;

    // Update download links
    document.getElementById('pdfDownloadLink').href = downloadUrl;
    document.getElementById('pdfDownloadBtn').href = downloadUrl;
    document.getElementById('pdfOpenNewTab').href = previewUrl;

    // Show the modal
    const modal = new bootstrap.Modal(document.getElementById('pdfPreviewModal'));
    modal.show();
}

// Clear iframe when modal is closed to stop loading
document.getElementById('pdfPreviewModal').addEventListener('hidden.bs.modal', function () {
    document.getElementById('pdfFrame').src = '';
});
</script>
{% endblock %}
