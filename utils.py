import os
import json
from datetime import datetime
from werkzeug.utils import secure_filename
from config import Config

# Try to import magic, but make it optional
try:
    import magic
    MAGIC_AVAILABLE = True
except ImportError:
    MAGIC_AVAILABLE = False

def allowed_file(filename):
    """Check if the uploaded file has an allowed extension."""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in Config.ALLOWED_EXTENSIONS

def is_pdf_file(file_path):
    """Verify that the uploaded file is actually a PDF."""
    if MAGIC_AVAILABLE:
        try:
            mime = magic.Magic(mime=True)
            file_type = mime.from_file(file_path)
            return file_type == 'application/pdf'
        except:
            # Fallback to extension check if magic fails
            pass

    # Simple file signature check for PDF
    try:
        with open(file_path, 'rb') as f:
            header = f.read(4)
            # PDF files start with %PDF
            return header == b'%PDF'
    except:
        # Final fallback to extension check
        return file_path.lower().endswith('.pdf')

def create_team_folder(team_name):
    """Create a folder for the team's submission."""
    # Create a safe folder name
    safe_team_name = secure_filename(team_name.replace(' ', '_'))
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    folder_name = f"{timestamp}_{safe_team_name}"
    
    folder_path = os.path.join(Config.UPLOAD_FOLDER, folder_name)
    os.makedirs(folder_path, exist_ok=True)
    
    return folder_path, folder_name

def save_submission_metadata(submission_data):
    """Save submission metadata to JSON file."""
    json_file_path = os.path.join(Config.DATA_FOLDER, 'submissions.json')
    
    # Load existing data or create new list
    if os.path.exists(json_file_path):
        with open(json_file_path, 'r', encoding='utf-8') as f:
            submissions = json.load(f)
    else:
        submissions = []
    
    # Add new submission
    submissions.append(submission_data)
    
    # Save updated data
    with open(json_file_path, 'w', encoding='utf-8') as f:
        json.dump(submissions, f, indent=2, ensure_ascii=False)

def validate_form_data(form_data):
    """Validate form data and return errors if any."""
    errors = []
    
    required_fields = ['team_name', 'college_name', 'proposed_title']
    for field in required_fields:
        if not form_data.get(field) or not form_data.get(field).strip():
            errors.append(f"{field.replace('_', ' ').title()} is required.")
    
    return errors
