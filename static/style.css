/* Custom styles for IIC Quest 3.0 Proposal Submission System */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    padding: 1.5rem;
}

.card-body {
    padding: 2rem;
}

.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.btn {
    border-radius: 8px;
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0056b3 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.3);
}

.alert {
    border-radius: 10px;
    border: none;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

footer {
    margin-top: auto;
}

/* File input styling */
.form-control[type="file"] {
    padding: 0.5rem;
}

.form-control[type="file"]::-webkit-file-upload-button {
    background: #0d6efd;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    margin-right: 1rem;
    cursor: pointer;
}

.form-control[type="file"]::-webkit-file-upload-button:hover {
    background: #0056b3;
}

/* Loading animation */
.fa-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* PDF Preview Modal */
#pdfPreviewModal .modal-body {
    height: 80vh;
}

#pdfFrame {
    height: 100% !important;
    min-height: 600px;
}

/* Button styling for action buttons */
.d-flex.gap-1 .btn {
    min-width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.25rem 0.5rem;
}

.d-flex.gap-1 {
    gap: 0.25rem !important;
}

/* Table responsive improvements */
.table-responsive {
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table {
    margin-bottom: 0;
    font-size: 0.9rem;
}

.table th {
    border-top: none;
    font-weight: 600;
    background-color: #343a40 !important;
    color: white !important;
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    white-space: nowrap;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    border-bottom: 1px solid #dee2e6;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Text truncation for table cells */
.text-truncate {
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Badge styling */
.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
}

/* Modal improvements */
.modal-fullscreen .modal-body {
    padding: 0;
}

.modal-fullscreen .modal-header,
.modal-fullscreen .modal-footer {
    padding: 1rem 1.5rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .card-body {
        padding: 1.5rem;
    }

    .container {
        padding: 0 1rem;
    }

    .d-flex.gap-1 {
        flex-direction: column !important;
        gap: 0.25rem !important;
    }

    .d-flex.gap-1 .btn {
        width: 100%;
        min-width: auto;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .table th,
    .table td {
        padding: 0.5rem 0.25rem;
    }

    #pdfPreviewModal .modal-body {
        height: 70vh;
    }
}
